import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Select,
  Button,
  Space,
  Pagination,
  Spin,
  Empty,
  Breadcrumb,
  Tag,
  Slider,
  Checkbox,
  Collapse,
  Typography,
  Input,
  message
} from 'antd';
import {
  FilterOutlined,
  AppstoreOutlined,
  BarsOutlined,
  SearchOutlined,
  ClearOutlined,
  HomeOutlined,
  BookOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useNavigate, useSearchParams } from 'react-router-dom';
import EnhancedBookCard from '../../components/business/EnhancedBookCard';
import AdvancedSearch from '../../components/business/AdvancedSearch';
import { booksService } from '../../services/books';
import { categoriesService } from '../../services/categories';

const { Title, Text } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

const BooksListContainer = styled.div`
  min-height: 100vh;
  background: #f5f5f5;
  
  .page-header {
    background: white;
    padding: 24px 0;
    border-bottom: 1px solid #f0f0f0;
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      .breadcrumb {
        margin-bottom: 16px;
      }
      
      .header-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .title-left {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .page-title {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
          }
          
          .result-count {
            color: #8c8c8c;
            font-size: 14px;
          }
        }
        
        .title-right {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .view-toggle {
            display: flex;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            overflow: hidden;
            
            .toggle-btn {
              padding: 8px 12px;
              border: none;
              background: white;
              cursor: pointer;
              transition: all 0.3s ease;
              
              &.active {
                background: #1677ff;
                color: white;
              }
              
              &:hover:not(.active) {
                background: #f5f5f5;
              }
            }
          }
        }
      }
    }
  }
  
  .page-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    display: flex;
    gap: 24px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      padding: 16px;
    }
    
    .filters-sidebar {
      width: 280px;
      flex-shrink: 0;
      
      @media (max-width: 768px) {
        width: 100%;
      }
      
      .filters-card {
        .filters-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          
          .filters-title {
            font-size: 16px;
            font-weight: 600;
          }
          
          .clear-filters {
            color: #8c8c8c;
            font-size: 12px;
            cursor: pointer;
            
            &:hover {
              color: #1677ff;
            }
          }
        }
        
        .filter-section {
          margin-bottom: 24px;
          
          .filter-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #262626;
          }
          
          .price-range {
            .ant-slider {
              margin: 16px 0;
            }
            
            .price-inputs {
              display: flex;
              gap: 8px;
              align-items: center;
              margin-top: 12px;
              
              .price-input {
                flex: 1;
              }
            }
          }
          
          .condition-options {
            .ant-checkbox-group {
              display: flex;
              flex-direction: column;
              gap: 8px;
            }
          }
          
          .category-list {
            max-height: 200px;
            overflow-y: auto;
            
            .category-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 0;
              cursor: pointer;
              border-radius: 4px;
              padding: 8px 12px;
              margin-bottom: 4px;
              transition: all 0.3s ease;
              
              &:hover {
                background: #f5f5f5;
              }
              
              &.active {
                background: #e6f4ff;
                color: #1677ff;
              }
              
              .category-name {
                flex: 1;
              }
              
              .category-count {
                font-size: 12px;
                color: #8c8c8c;
              }
            }
          }
        }
      }
    }
    
    .books-content {
      flex: 1;
      
      .content-header {
        background: white;
        padding: 16px 20px;
        border-radius: 8px;
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .search-section {
          flex: 1;
          max-width: 400px;
        }
        
        .sort-section {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .sort-label {
            color: #8c8c8c;
            font-size: 14px;
          }
          
          .sort-select {
            min-width: 150px;
          }
        }
      }
      
      .books-grid {
        display: grid;
        gap: 20px;
        
        &.grid-view {
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        }
        
        &.list-view {
          grid-template-columns: 1fr;
          
          .book-card {
            .ant-card-body {
              padding: 16px;
            }
          }
        }
      }
      
      .pagination-container {
        margin-top: 32px;
        text-align: center;
        
        .pagination-info {
          margin-bottom: 16px;
          color: #8c8c8c;
          font-size: 14px;
        }
      }
      
      .empty-state {
        text-align: center;
        padding: 60px 20px;
        background: white;
        border-radius: 8px;
        
        .empty-icon {
          font-size: 64px;
          color: #d9d9d9;
          margin-bottom: 16px;
        }
        
        .empty-title {
          font-size: 18px;
          color: #595959;
          margin-bottom: 8px;
        }
        
        .empty-desc {
          color: #8c8c8c;
          margin-bottom: 24px;
        }
      }
    }
  }
`;

interface BooksListPageProps {}

const BooksListPage: React.FC<BooksListPageProps> = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [loading, setLoading] = useState(false);
  const [books, setBooks] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  
  const [filters, setFilters] = useState({
    keyword: searchParams.get('keyword') || '',
    category: searchParams.get('category') || '',
    minPrice: 0,
    maxPrice: 1000,
    condition: [] as string[],
    sort: searchParams.get('sort') || 'created_at_DESC'
  });
  
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    loadBooks();
  }, [filters, pagination.current]);

  const loadCategories = async () => {
    try {
      const response = await categoriesService.getCategories();
      if (response.success && response.data) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  const loadBooks = async () => {
    try {
      setLoading(true);
      
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        keyword: filters.keyword || undefined,
        category_id: filters.category || undefined,
        min_price: filters.minPrice > 0 ? filters.minPrice : undefined,
        max_price: filters.maxPrice < 1000 ? filters.maxPrice : undefined,
        condition: filters.condition.length > 0 ? filters.condition[0] : undefined,
        sort: filters.sort
      };

      const response = await booksService.getBooks(params);
      
      if (response.success) {
        setBooks(response.data.books);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.total_items
        }));
      }
    } catch (error) {
      message.error('加载图书列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (searchFilters: any) => {
    setFilters(prev => ({
      ...prev,
      ...searchFilters
    }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleClearFilters = () => {
    setFilters({
      keyword: '',
      category: '',
      minPrice: 0,
      maxPrice: 1000,
      condition: [],
      sort: 'created_at_DESC'
    });
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize
    }));
  };

  const sortOptions = [
    { label: '最新发布', value: 'created_at_DESC' },
    { label: '价格从低到高', value: 'price_ASC' },
    { label: '价格从高到低', value: 'price_DESC' },
    { label: '销量从高到低', value: 'sales_count_DESC' },
    { label: '评分从高到低', value: 'rating_DESC' }
  ];

  const conditionOptions = [
    { label: '全新', value: '全新' },
    { label: '九成新', value: '九成新' },
    { label: '八成新', value: '八成新' },
    { label: '七成新', value: '七成新' },
    { label: '六成新', value: '六成新' }
  ];

  return (
    <BooksListContainer>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <Breadcrumb className="breadcrumb">
            <Breadcrumb.Item>
              <HomeOutlined />
              <span onClick={() => navigate('/')} style={{ cursor: 'pointer', marginLeft: 8 }}>
                首页
              </span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <BookOutlined />
              图书列表
            </Breadcrumb.Item>
            {filters.category && (
              <Breadcrumb.Item>
                {categories.find(c => c.id === filters.category)?.name}
              </Breadcrumb.Item>
            )}
          </Breadcrumb>
          
          <div className="header-title">
            <div className="title-left">
              <Title level={2} className="page-title">图书列表</Title>
              <span className="result-count">
                共找到 {pagination.total} 本图书
              </span>
            </div>
            <div className="title-right">
              <div className="view-toggle">
                <button
                  className={`toggle-btn ${viewMode === 'grid' ? 'active' : ''}`}
                  onClick={() => setViewMode('grid')}
                >
                  <AppstoreOutlined />
                </button>
                <button
                  className={`toggle-btn ${viewMode === 'list' ? 'active' : ''}`}
                  onClick={() => setViewMode('list')}
                >
                  <BarsOutlined />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 页面内容 */}
      <div className="page-content">
        {/* 筛选侧边栏 */}
        <div className="filters-sidebar">
          <Card className="filters-card">
            <div className="filters-header">
              <span className="filters-title">
                <FilterOutlined style={{ marginRight: 8 }} />
                筛选条件
              </span>
              <span className="clear-filters" onClick={handleClearFilters}>
                <ClearOutlined style={{ marginRight: 4 }} />
                清空
              </span>
            </div>

            {/* 高级搜索 */}
            <div className="filter-section">
              <AdvancedSearch
                onSearch={handleSearch}
                initialFilters={filters}
              />
            </div>

            {/* 分类筛选 */}
            <div className="filter-section">
              <div className="filter-title">图书分类</div>
              <div className="category-list">
                <div
                  className={`category-item ${!filters.category ? 'active' : ''}`}
                  onClick={() => handleFilterChange('category', '')}
                >
                  <span className="category-name">全部分类</span>
                </div>
                {categories.map(category => (
                  <div
                    key={category.id}
                    className={`category-item ${filters.category === category.id ? 'active' : ''}`}
                    onClick={() => handleFilterChange('category', category.id)}
                  >
                    <span className="category-name">{category.name}</span>
                    <span className="category-count">({category.book_count || 0})</span>
                  </div>
                ))}
              </div>
            </div>

            {/* 价格范围 */}
            <div className="filter-section">
              <div className="filter-title">价格范围</div>
              <div className="price-range">
                <Slider
                  range
                  min={0}
                  max={1000}
                  value={[filters.minPrice, filters.maxPrice]}
                  onChange={([min, max]) => {
                    handleFilterChange('minPrice', min);
                    handleFilterChange('maxPrice', max);
                  }}
                  marks={{
                    0: '¥0',
                    200: '¥200',
                    500: '¥500',
                    1000: '¥1000+'
                  }}
                />
                <div className="price-inputs">
                  <Input
                    className="price-input"
                    prefix="¥"
                    value={filters.minPrice}
                    onChange={(e) => handleFilterChange('minPrice', Number(e.target.value) || 0)}
                  />
                  <Text>至</Text>
                  <Input
                    className="price-input"
                    prefix="¥"
                    value={filters.maxPrice}
                    onChange={(e) => handleFilterChange('maxPrice', Number(e.target.value) || 1000)}
                  />
                </div>
              </div>
            </div>

            {/* 图书状况 */}
            <div className="filter-section">
              <div className="filter-title">图书状况</div>
              <div className="condition-options">
                <Checkbox.Group
                  options={conditionOptions}
                  value={filters.condition}
                  onChange={(values) => handleFilterChange('condition', values)}
                />
              </div>
            </div>
          </Card>
        </div>

        {/* 图书内容区 */}
        <div className="books-content">
          {/* 内容头部 */}
          <div className="content-header">
            <div className="search-section">
              <Input.Search
                placeholder="搜索图书名称、作者、ISBN..."
                value={filters.keyword}
                onChange={(e) => handleFilterChange('keyword', e.target.value)}
                onSearch={() => loadBooks()}
                enterButton={<SearchOutlined />}
              />
            </div>
            
            <div className="sort-section">
              <span className="sort-label">排序:</span>
              <Select
                className="sort-select"
                value={filters.sort}
                onChange={(value) => handleFilterChange('sort', value)}
              >
                {sortOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </div>
          </div>

          {/* 图书网格 */}
          <Spin spinning={loading}>
            {books.length > 0 ? (
              <div className={`books-grid ${viewMode}-view`}>
                {books.map(book => (
                  <EnhancedBookCard
                    key={book.id}
                    book={book}
                  />
                ))}
              </div>
            ) : (
              <div className="empty-state">
                <BookOutlined className="empty-icon" />
                <div className="empty-title">暂无图书</div>
                <div className="empty-desc">
                  {filters.keyword || filters.category ? 
                    '没有找到符合条件的图书，请尝试调整筛选条件' : 
                    '暂时没有图书，请稍后再来看看'
                  }
                </div>
                <Button type="primary" onClick={handleClearFilters}>
                  清空筛选条件
                </Button>
              </div>
            )}
          </Spin>

          {/* 分页 */}
          {books.length > 0 && (
            <div className="pagination-container">
              <div className="pagination-info">
                显示第 {(pagination.current - 1) * pagination.pageSize + 1} - {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共 {pagination.total} 条
              </div>
              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                showSizeChanger
                showQuickJumper
                pageSizeOptions={['10', '20', '50', '100']}
                onChange={handlePageChange}
                onShowSizeChange={handlePageChange}
              />
            </div>
          )}
        </div>
      </div>
    </BooksListContainer>
  );
};

export default BooksListPage;
